import { chromium } from "playwright";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>pt<PERSON>, <PERSON>rap<PERSON><PERSON><PERSON>ult } from "./types";

export const playwrightAdapter: ScraperAdapter = {
  name: 'playwright',
  async scrape(url: string): Promise<ScrapeResult> {
    let browser;
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        viewport: { width: 1280, height: 720 }
      });

      // Try multiple wait strategies
      try {
        // First try with domcontentloaded (faster)
        console.log(`[PLAYWRIGHT] Navigating to ${url} with domcontentloaded`);
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 20000 });

        // Wait a bit for dynamic content
        await page.waitForTimeout(2000);
        console.log(`[PLAYWRIGHT] Page loaded successfully`);
      } catch (loadError) {
        const loadErrorMsg = loadError instanceof Error ? loadError.message : String(loadError);
        console.log(`[PLAYWRIGHT] Load event failed (${loadErrorMsg}), trying alternative approach`);

        // If initial load fails, try with load event and longer wait
        try {
          console.log(`[PLAYWRIGHT] Trying with load event`);
          await page.goto(url, { waitUntil: 'load', timeout: 25000 });
          await page.waitForTimeout(3000);
        } catch (basicError) {
          const basicErrorMsg = basicError instanceof Error ? basicError.message : String(basicError);
          console.log(`[PLAYWRIGHT] Basic navigation failed (${basicErrorMsg})`);
          // Last resort: just navigate and wait
          try {
            await page.goto(url, { timeout: 30000 });
            await page.waitForTimeout(5000);
          } catch (finalError) {
            const finalErrorMsg = finalError instanceof Error ? finalError.message : String(finalError);
            console.log(`[PLAYWRIGHT] Final navigation attempt failed (${finalErrorMsg})`);
            throw finalError;
          }
        }
      }

      const html = await page.content();

      if (!html || html.trim() === '') {
        return { html: null, error: 'Empty page content after JS execution' };
      }

      return { html };
    } catch (error) {
      return { html: null, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};
