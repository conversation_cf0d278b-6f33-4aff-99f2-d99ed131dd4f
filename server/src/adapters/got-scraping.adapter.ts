import { gotScraping } from "got-scraping";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>pt<PERSON>, Scrape<PERSON><PERSON>ult } from "./types";

export const gotScrapingAdapter: ScraperAdapter = {
  name: 'gotScraping',
  async scrape(url: string): Promise<ScrapeResult> {
    try {
      // Use got-scraping with minimal configuration to leverage its anti-detection features
      const response = await gotScraping.get(url, {
        timeout: { request: 15000 } // Only increase timeout, let got-scraping handle the rest
      });

      const html = response.body;

      if (!html || html.trim() === '') {
        return { html: null, error: 'Empty response body' };
      }

      return { html };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.log(`[GOT-SCRAPING] Failed for ${url}: ${errorMessage}`);
      return { html: null, error: errorMessage };
    }
  }
};
