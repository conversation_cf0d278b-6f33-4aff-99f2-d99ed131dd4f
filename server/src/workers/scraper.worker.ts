import { parentPort, workerData } from "worker_threads";
import { getAdapter } from "../adapters";
import { getParser, setGeminiA<PERSON><PERSON><PERSON> } from "../parsers";
import type { ScrapeResult } from "../adapters/types";
import type { ParseResult } from "../parsers/types";
import type { Scraper } from "@shared/schema";

interface WorkerJobData {
  id: string;
  scraper: Scraper;
  primaryAdapter: string;
  fallbackAdapter: string;
  geminiApiKey?: string; // Optional API key from UI
}

interface WorkerResult {
  price: string | null;
  error?: string;
}

async function run() {
  if (!parentPort) throw new Error("This script must be run as a worker thread.");

  const { id, scraper, primaryAdapter, fallbackAdapter, geminiApiKey }: WorkerJobData = workerData;

  console.log(`[WORKER] Job ${id}: Starting scrape for ${scraper.url}`);

  try {
    // Set Gemini API key if provided from UI
    if (geminiApiKey) {
      setGeminiApiKey(geminiApiKey);
    }

    // Step 1: Try primary adapter (got-scraping)
    console.log(`[WORKER] Job ${id}: Step 1 - Trying primary adapter (${primaryAdapter})`);
    const primaryAdapterInstance = getAdapter(primaryAdapter);

    if (!primaryAdapterInstance) {
      throw new Error(`Primary adapter "${primaryAdapter}" not found.`);
    }

    let scrapeResult: ScrapeResult = await primaryAdapterInstance.scrape(scraper.url);

    // Step 2: If primary fails, try fallback adapter (playwright)
    if (!scrapeResult.html) {
      console.log(`[WORKER] Job ${id}: Step 2 - Primary adapter failed (${scrapeResult.error || 'no HTML content'}). Trying fallback adapter (${fallbackAdapter})`);

      const fallbackAdapterInstance = getAdapter(fallbackAdapter);
      if (!fallbackAdapterInstance) {
        throw new Error(`Fallback adapter "${fallbackAdapter}" not found.`);
      }

      scrapeResult = await fallbackAdapterInstance.scrape(scraper.url);

      if (!scrapeResult.html) {
        console.log(`[WORKER] Job ${id}: Step 3 - Both adapters failed. Final error: ${scrapeResult.error || 'no HTML content'}`);
        const finalResult: WorkerResult = {
          price: null,
          error: `Both ${primaryAdapter} and ${fallbackAdapter} failed to get page content. Last error: ${scrapeResult.error || 'no HTML content'}`,
        };
        parentPort.postMessage(finalResult);
        return;
      } else {
        console.log(`[WORKER] Job ${id}: Step 3 - Fallback adapter succeeded! HTML content retrieved`);
      }
    } else {
      console.log(`[WORKER] Job ${id}: Step 2 - Primary adapter succeeded! HTML content retrieved`);
    }

    // Step 3: Parse the price using the appropriate parser
    console.log(`[WORKER] Job ${id}: Step 4 - Parsing price using ${scraper.parser} parser`);
    const parser = getParser(scraper.parser);

    if (!parser) {
      throw new Error(`Parser "${scraper.parser}" not found.`);
    }

    const parseResult: ParseResult = await parser.parse(scrapeResult.html!, scraper.itemName, scraper.selector);

    if (parseResult.price) {
      console.log(`[WORKER] Job ${id}: Step 5 - Price parsing succeeded! Price extracted: ${parseResult.price}`);
    } else {
      console.log(`[WORKER] Job ${id}: Step 5 - Price parsing failed: ${parseResult.error || 'no price found'}`);
    }

    const finalResult: WorkerResult = {
      price: parseResult.price,
      error: parseResult.error,
    };

    console.log(`[WORKER] Job ${id}: Completed successfully, sending result to main thread`);
    parentPort.postMessage(finalResult);

  } catch (error) {
    console.error(`Job ${id}: Worker execution failed:`, error);

    const result: WorkerResult = {
      price: null,
      error: error instanceof Error ? error.message : 'Worker execution failed',
    };

    parentPort.postMessage(result);
  }
}

run();
