import type { Server as HttpServer } from "http";
import { Server, Socket } from "socket.io";
import { storage } from "../storage";
import { scraperQueue, type ScrapeJob, type WorkerResult } from "./workers/pool";
import type { Scraper } from "@shared/schema";

export function setupSockets(server: HttpServer) {
  const io = new Server(server, {
    cors: {
      origin: "*", // Configure this for production
    },
  });

  io.on("connection", (socket: Socket) => {
    console.log(`Client connected: ${socket.id}`);

    socket.on("disconnect", () => {
      console.log(`Client disconnected: ${socket.id}`);
    });

    // Handler for updating a single scraper
    socket.on("scraper:update", async (id: string, geminiApiKey?: string) => {
      await processScraperUpdate(id, socket, 'high', geminiApiKey); // High priority for single updates
    });

    // Handler for updating all scrapers
    socket.on("scraper:update-all", async (geminiApiKey?: string) => {
      const scrapers = await storage.getAllScrapers();
      console.log(`Processing bulk update for ${scrapers.length} scrapers`);

      // Process all updates in parallel using the queue
      for (const scraper of scrapers) {
        processScraperUpdate(scraper.id, socket, 'normal', geminiApiKey); // Normal priority for bulk updates
      }
    });

    // Handler for getting queue statistics
    socket.on("queue:stats", () => {
      const stats = scraperQueue.getStats();
      socket.emit("queue:stats", stats);
    });
  });
}

async function processScraperUpdate(id: string, socket: Socket, priority: 'high' | 'normal' = 'normal', geminiApiKey?: string) {
  let scraper = await storage.getScraper(id);
  if (!scraper) {
    socket.emit("scraper:error", { id, message: "Scraper not found" });
    return;
  }

  // 1. Set status to "updating" and notify client
  await storage.updateScraper(id, { status: "updating" });
  let updatedScraper = await storage.getScraper(id);
  socket.emit("scraper:updated", updatedScraper);

  // 2. Create and submit job to the queue
  const job: ScrapeJob = {
    id: `scraper-${id}-${Date.now()}`,
    scraper,
    priority,
    geminiApiKey, // Pass the API key from UI if provided
    onProgress: (status: string) => {
      console.log(`Job ${job.id}: ${status}`);
      // Optionally emit progress updates to client
      socket.emit("scraper:progress", { id, status });
    },
    onComplete: async (result: WorkerResult) => {
      await handleScrapingResult(id, result, socket);
    },
    onError: async (error: Error) => {
      console.error(`Job ${job.id} failed:`, error);
      await handleScrapingError(id, error, socket);
    }
  };

  try {
    // Submit job to queue (this returns immediately)
    scraperQueue.addJob(job);
    console.log(`[SOCKET] Submitted scraping job ${job.id} for scraper ${id} (priority: ${priority})`);
  } catch (error) {
    console.error(`[SOCKET] Failed to submit job for scraper ${id}:`, error);
    await handleScrapingError(id, error instanceof Error ? error : new Error(String(error)), socket);
  }
}

// Handle successful scraping result
async function handleScrapingResult(scraperId: string, result: WorkerResult, socket: Socket) {
  try {
    const scraper = await storage.getScraper(scraperId);
    if (!scraper) {
      socket.emit("scraper:error", { id: scraperId, message: "Scraper not found during result processing" });
      return;
    }

    if (result.price) {
      const currentPrice = parseFloat(result.price);
      const lowestPrice = scraper.lowestPrice
        ? Math.min(parseFloat(scraper.lowestPrice), currentPrice)
        : currentPrice;

      // Add price history first (this will also update the average price in the database)
      await storage.addPriceHistory(scraperId, result.price);

      await storage.updateScraper(scraperId, {
        currentPrice: result.price,
        lowestPrice: lowestPrice.toString(),
        status: "active",
        lastError: null,
      });

      console.log(`[SOCKET] Scraper ${scraperId}: Successfully updated price to ${result.price}`);
    } else {
      await storage.updateScraper(scraperId, {
        status: "error",
        lastError: result.error || "Failed to extract price from both adapters.",
      });

      console.log(`[SOCKET] Scraper ${scraperId}: Failed to extract price - ${result.error}`);
    }

    // Send the final state to the client
    const updatedScraper = await storage.getScraper(scraperId);
    socket.emit("scraper:updated", updatedScraper);

  } catch (error) {
    console.error(`Error processing result for scraper ${scraperId}:`, error);
    await handleScrapingError(scraperId, error instanceof Error ? error : new Error(String(error)), socket);
  }
}

// Handle scraping error
async function handleScrapingError(scraperId: string, error: Error, socket: Socket) {
  try {
    await storage.updateScraper(scraperId, {
      status: "error",
      lastError: error.message,
    });

    const updatedScraper = await storage.getScraper(scraperId);
    socket.emit("scraper:updated", updatedScraper);

    console.log(`[SOCKET] Scraper ${scraperId}: Error handled - ${error.message}`);
  } catch (dbError) {
    console.error(`[SOCKET] Failed to update scraper ${scraperId} with error state:`, dbError);
    socket.emit("scraper:error", { id: scraperId, message: "Failed to update scraper status" });
  }
}
