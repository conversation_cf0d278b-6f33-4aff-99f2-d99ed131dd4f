import * as cheerio from "cheerio";
import { parsePrice } from "../utils/price-parser";
import type { PriceParser, ParseR<PERSON>ult } from "./types";

export const staticParser: PriceParser = {
  name: "static",
  async parse(html: string, itemName: string, selector?: string): Promise<ParseResult> {
    if (!selector) {
      return { price: null, error: "CSS selector is required for static parser" };
    }

    try {
      const $ = cheerio.load(html);
      const priceElement = $(selector);
      
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page` };
      }

      const priceText = priceElement.first().text().trim();
      const price = parsePrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { price: null, error: errorMessage };
    }
  }
};
