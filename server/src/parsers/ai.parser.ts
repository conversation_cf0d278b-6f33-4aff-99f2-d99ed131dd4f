import type { PriceParser, ParseR<PERSON>ult } from "./types";

// Global variable to store UI-provided API key
let uiApiKey: string | null = null;

export function setGeminiApiKey(key: string | null) {
  uiApiKey = key;
}

export const aiParser: PriceParser = {
  name: "ai",
  async parse(html: string, itemName: string, selector?: string): Promise<ParseResult> {
    try {
      // Prioritize UI-provided key over environment variable
      const apiKey = uiApiKey || process.env.GEMINI_API_KEY;

      if (!apiKey) {
        return { price: null, error: "Gemini API key not found. Please set GEMINI_API_KEY environment variable or provide it via UI." };
      }

      // Prepare the prompt for Gemini
      const prompt = `${html}

Task: Find the price of the ${itemName}. Reply only with the price and nothing else in the format: [whole part].[decimal part]`;

      // Call Gemini API using gemini-1.5-flash model
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: 0,
            topK: 1,
            topP: 1,
            maxOutputTokens: 50,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { price: null, error: `Gemini API error: ${response.status} - ${errorText}` };
      }

      const data = await response.json();
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
        return { price: null, error: "Invalid response format from Gemini API" };
      }

      const priceText = data.candidates[0].content.parts[0].text.trim();
      
      // Validate that the response is a valid number
      const priceNumber = parseFloat(priceText);
      if (isNaN(priceNumber) || priceNumber < 0) {
        return { price: null, error: `AI returned invalid price: "${priceText}"` };
      }

      return { price: priceNumber.toString() };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { price: null, error: `AI parser error: ${errorMessage}` };
    }
  }
};
