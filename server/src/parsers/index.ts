import { staticParser } from "./static.parser";
import { aiParser, setGeminiApi<PERSON>ey } from "./ai.parser";
import type { PriceParser } from "./types";

const parsers = new Map<string, PriceParser>();

// Register all available parsers
parsers.set(staticParser.name, staticParser);
parsers.set(aiParser.name, aiParser);

export function getParser(name: string): PriceParser | undefined {
  return parsers.get(name);
}

export { staticParser, aiParser, setGeminiApiKey };
export type { PriceParser, ParseResult } from "./types";
