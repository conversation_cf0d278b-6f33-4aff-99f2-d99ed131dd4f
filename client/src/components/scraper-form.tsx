import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Zap, Download, RotateCw, Loader2, Settings } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { socket } from "@/lib/socket";
import { insertScraperSchema } from "@shared/schema";
import type { InsertScraper } from "@shared/schema";
import SettingsDialog from "@/components/settings-dialog";

export default function ScraperForm() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<InsertScraper>({
    resolver: zodResolver(insertScraperSchema),
    defaultValues: {
      itemName: "",
      url: "",
      selector: "",
      parser: "static",
    },
  });

  const createScraperMutation = useMutation({
    mutationFn: async (data: InsertScraper) => {
      const res = await apiRequest("POST", "/api/scrapers", data);
      return res.json();
    },
    onSuccess: async (newScraper) => {
      queryClient.invalidateQueries({ queryKey: ["/api/scrapers"] });
      form.reset();

      // Immediately trigger update for the new scraper using socket
      const geminiApiKey = localStorage.getItem("gemini-api-key");
      socket.emit("scraper:update", newScraper.id, geminiApiKey);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add scraper",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateAllMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/scrapers/update-all");
      return res.json();
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["/api/scrapers"] });
      
      const { results } = response;
      const successCount = results.filter((r: any) => r.success).length;
      const failureCount = results.filter((r: any) => !r.success).length;
      
      if (failureCount === 0) {
        toast({
          title: "All scrapers updated",
          description: `Successfully updated ${successCount} scrapers.`,
        });
      } else if (successCount === 0) {
        toast({
          title: "Updates failed",
          description: `Failed to update all ${failureCount} scrapers.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Partial success",
          description: `Updated ${successCount} scrapers, ${failureCount} failed.`,
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update scrapers",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertScraper) => {
    createScraperMutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="text-primary" size={20} />
            Add New Scraper
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <Label htmlFor="itemName">Item Name</Label>
              <Input
                id="itemName"
                data-testid="input-item-name"
                placeholder="e.g., iPhone 15 Pro"
                {...form.register("itemName")}
                className="mt-2"
              />
              {form.formState.errors.itemName && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.itemName.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="url">Target URL</Label>
              <Input
                id="url"
                data-testid="input-url"
                type="url"
                placeholder="https://example.com/product-page"
                {...form.register("url")}
                className="mt-2"
              />
              {form.formState.errors.url && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.url.message}
                </p>
              )}
            </div>

            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="parser">Parser Type</Label>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="parser-switch" className="text-sm font-normal">
                    {form.watch("parser") === "static" ? "Static (CSS)" : "AI (Gemini)"}
                  </Label>
                  <Switch
                    id="parser-switch"
                    checked={form.watch("parser") === "ai"}
                    onCheckedChange={(checked) => {
                      form.setValue("parser", checked ? "ai" : "static");
                      // Clear selector when switching to AI
                      if (checked) {
                        form.setValue("selector", "");
                      }
                    }}
                  />
                </div>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Choose between CSS selector-based parsing or AI-powered parsing using Gemini
              </p>
            </div>

            {form.watch("parser") === "static" && (
              <div>
                <Label htmlFor="selector">Price Selector</Label>
              <Input
                id="selector"
                data-testid="input-selector"
                placeholder=".price, #cost, [data-price]"
                {...form.register("selector")}
                className="mt-2 font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground mt-1">
                CSS selector for the price element
              </p>
              {form.formState.errors.selector && (
                <p className="text-sm text-destructive mt-1">
                  {form.formState.errors.selector.message}
                </p>
              )}
              </div>
            )}

            <Button
              type="submit"
              data-testid="button-add-scraper"
              disabled={createScraperMutation.isPending}
              className="w-full"
            >
              {createScraperMutation.isPending ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Plus className="w-4 h-4 mr-2" />
              )}
              {createScraperMutation.isPending ? "Adding..." : "Add Scraper"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="text-chart-3" size={20} />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button
            data-testid="button-update-all"
            onClick={() => updateAllMutation.mutate()}
            disabled={updateAllMutation.isPending}
            variant="secondary"
            className="w-full"
          >
            {updateAllMutation.isPending ? (
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
            ) : (
              <RotateCw className="w-4 h-4 mr-2" />
            )}
            {updateAllMutation.isPending ? "Updating..." : "Update All Scrapers"}
          </Button>

          <Button
            data-testid="button-export-data"
            variant="ghost"
            className="w-full"
            onClick={() => {
              toast({
                title: "Export functionality",
                description: "Export feature coming soon!",
              });
            }}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Data
          </Button>

          <SettingsDialog>
            <Button
              data-testid="button-settings"
              variant="ghost"
              className="w-full"
            >
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </SettingsDialog>
        </CardContent>
      </Card>
    </div>
  );
}
