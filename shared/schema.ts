import { z } from "zod";

// TypeScript interfaces for the data models
export interface Scraper {
  id: string;
  itemName: string;
  url: string;
  selector?: string; // Optional for AI parsers, required for static parsers
  parser: "static" | "ai"; // Parser type: static (CSS selector) or ai (Gemini-powered)
  currentPrice: string | null;
  lowestPrice: string | null;
  averagePrice: string | null;
  status: "active" | "updating" | "error";
  lastUpdated: Date;
  lastError: string | null;
  createdAt: Date;
  // Sale tracking fields
  isOnSale: boolean;
  saleStartDate: Date | null;
  priceBeforeSale: string | null;
  saleThreshold: number; // Percentage threshold for sale detection (e.g., 0.15 for 15%)
}

export interface PriceHistory {
  id: string;
  scraperId: string;
  price: string;
  timestamp: Date;
}

// Zod schemas for validation
export const insertScraperSchema = z.object({
  itemName: z.string().min(1, "Item name is required"),
  url: z.string().url("Must be a valid URL"),
  selector: z.string().min(1, "CSS selector is required").optional(),
  parser: z.enum(["static", "ai"]).default("static"),
}).refine((data) => {
  // If parser is static, selector is required
  if (data.parser === "static" && (!data.selector || data.selector.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "CSS selector is required when using static parser",
  path: ["selector"],
});

export const updateScraperSchema = z.object({
  itemName: z.string().min(1, "Item name is required").optional(),
  url: z.string().url("Must be a valid URL").optional(),
  selector: z.string().min(1, "CSS selector is required").optional(),
  parser: z.enum(["static", "ai"]).optional(),
  currentPrice: z.string().nullable().optional(),
  lowestPrice: z.string().nullable().optional(),
  averagePrice: z.string().nullable().optional(),
  status: z.enum(["active", "updating", "error"]).optional(),
  lastError: z.string().nullable().optional(),
  // Sale tracking fields
  isOnSale: z.boolean().optional(),
  saleStartDate: z.date().nullable().optional(),
  priceBeforeSale: z.string().nullable().optional(),
  saleThreshold: z.number().min(0).max(1).optional(), // 0-1 range for percentage
}).refine((data) => {
  // If parser is being updated to static, selector must be provided or already exist
  if (data.parser === "static" && data.selector !== undefined && (!data.selector || data.selector.trim() === "")) {
    return false;
  }
  return true;
}, {
  message: "CSS selector is required when using static parser",
  path: ["selector"],
});

// Type inference from schemas
export type InsertScraper = z.infer<typeof insertScraperSchema>;
export type UpdateScraper = z.infer<typeof updateScraperSchema>;
